// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SearchState {
  String get query => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get hasMore => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  List<Map<String, dynamic>> get products => throw _privateConstructorUsedError;
  List<Map<String, dynamic>> get categories =>
      throw _privateConstructorUsedError;
  List<Map<String, dynamic>> get subcategories =>
      throw _privateConstructorUsedError;
  List<Map<String, dynamic>> get stores => throw _privateConstructorUsedError;
  List<String> get recentSearches => throw _privateConstructorUsedError;
  List<Map<String, dynamic>> get suggestions =>
      throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SearchStateCopyWith<SearchState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchStateCopyWith<$Res> {
  factory $SearchStateCopyWith(
          SearchState value, $Res Function(SearchState) then) =
      _$SearchStateCopyWithImpl<$Res, SearchState>;
  @useResult
  $Res call(
      {String query,
      bool isLoading,
      bool hasMore,
      int page,
      List<Map<String, dynamic>> products,
      List<Map<String, dynamic>> categories,
      List<Map<String, dynamic>> subcategories,
      List<Map<String, dynamic>> stores,
      List<String> recentSearches,
      List<Map<String, dynamic>> suggestions,
      String? error});
}

/// @nodoc
class _$SearchStateCopyWithImpl<$Res, $Val extends SearchState>
    implements $SearchStateCopyWith<$Res> {
  _$SearchStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
    Object? isLoading = null,
    Object? hasMore = null,
    Object? page = null,
    Object? products = null,
    Object? categories = null,
    Object? subcategories = null,
    Object? stores = null,
    Object? recentSearches = null,
    Object? suggestions = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      query: null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      products: null == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      categories: null == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      subcategories: null == subcategories
          ? _value.subcategories
          : subcategories // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      stores: null == stores
          ? _value.stores
          : stores // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      recentSearches: null == recentSearches
          ? _value.recentSearches
          : recentSearches // ignore: cast_nullable_to_non_nullable
              as List<String>,
      suggestions: null == suggestions
          ? _value.suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SearchStateImplCopyWith<$Res>
    implements $SearchStateCopyWith<$Res> {
  factory _$$SearchStateImplCopyWith(
          _$SearchStateImpl value, $Res Function(_$SearchStateImpl) then) =
      __$$SearchStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String query,
      bool isLoading,
      bool hasMore,
      int page,
      List<Map<String, dynamic>> products,
      List<Map<String, dynamic>> categories,
      List<Map<String, dynamic>> subcategories,
      List<Map<String, dynamic>> stores,
      List<String> recentSearches,
      List<Map<String, dynamic>> suggestions,
      String? error});
}

/// @nodoc
class __$$SearchStateImplCopyWithImpl<$Res>
    extends _$SearchStateCopyWithImpl<$Res, _$SearchStateImpl>
    implements _$$SearchStateImplCopyWith<$Res> {
  __$$SearchStateImplCopyWithImpl(
      _$SearchStateImpl _value, $Res Function(_$SearchStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
    Object? isLoading = null,
    Object? hasMore = null,
    Object? page = null,
    Object? products = null,
    Object? categories = null,
    Object? subcategories = null,
    Object? stores = null,
    Object? recentSearches = null,
    Object? suggestions = null,
    Object? error = freezed,
  }) {
    return _then(_$SearchStateImpl(
      query: null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      products: null == products
          ? _value._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      categories: null == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      subcategories: null == subcategories
          ? _value._subcategories
          : subcategories // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      stores: null == stores
          ? _value._stores
          : stores // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      recentSearches: null == recentSearches
          ? _value._recentSearches
          : recentSearches // ignore: cast_nullable_to_non_nullable
              as List<String>,
      suggestions: null == suggestions
          ? _value._suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SearchStateImpl implements _SearchState {
  const _$SearchStateImpl(
      {required this.query,
      required this.isLoading,
      required this.hasMore,
      required this.page,
      required final List<Map<String, dynamic>> products,
      required final List<Map<String, dynamic>> categories,
      required final List<Map<String, dynamic>> subcategories,
      required final List<Map<String, dynamic>> stores,
      required final List<String> recentSearches,
      required final List<Map<String, dynamic>> suggestions,
      this.error})
      : _products = products,
        _categories = categories,
        _subcategories = subcategories,
        _stores = stores,
        _recentSearches = recentSearches,
        _suggestions = suggestions;

  @override
  final String query;
  @override
  final bool isLoading;
  @override
  final bool hasMore;
  @override
  final int page;
  final List<Map<String, dynamic>> _products;
  @override
  List<Map<String, dynamic>> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  final List<Map<String, dynamic>> _categories;
  @override
  List<Map<String, dynamic>> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  final List<Map<String, dynamic>> _subcategories;
  @override
  List<Map<String, dynamic>> get subcategories {
    if (_subcategories is EqualUnmodifiableListView) return _subcategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subcategories);
  }

  final List<Map<String, dynamic>> _stores;
  @override
  List<Map<String, dynamic>> get stores {
    if (_stores is EqualUnmodifiableListView) return _stores;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_stores);
  }

  final List<String> _recentSearches;
  @override
  List<String> get recentSearches {
    if (_recentSearches is EqualUnmodifiableListView) return _recentSearches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentSearches);
  }

  final List<Map<String, dynamic>> _suggestions;
  @override
  List<Map<String, dynamic>> get suggestions {
    if (_suggestions is EqualUnmodifiableListView) return _suggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_suggestions);
  }

  @override
  final String? error;

  @override
  String toString() {
    return 'SearchState(query: $query, isLoading: $isLoading, hasMore: $hasMore, page: $page, products: $products, categories: $categories, subcategories: $subcategories, stores: $stores, recentSearches: $recentSearches, suggestions: $suggestions, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchStateImpl &&
            (identical(other.query, query) || other.query == query) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.page, page) || other.page == page) &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality()
                .equals(other._subcategories, _subcategories) &&
            const DeepCollectionEquality().equals(other._stores, _stores) &&
            const DeepCollectionEquality()
                .equals(other._recentSearches, _recentSearches) &&
            const DeepCollectionEquality()
                .equals(other._suggestions, _suggestions) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      query,
      isLoading,
      hasMore,
      page,
      const DeepCollectionEquality().hash(_products),
      const DeepCollectionEquality().hash(_categories),
      const DeepCollectionEquality().hash(_subcategories),
      const DeepCollectionEquality().hash(_stores),
      const DeepCollectionEquality().hash(_recentSearches),
      const DeepCollectionEquality().hash(_suggestions),
      error);

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchStateImplCopyWith<_$SearchStateImpl> get copyWith =>
      __$$SearchStateImplCopyWithImpl<_$SearchStateImpl>(this, _$identity);
}

abstract class _SearchState implements SearchState {
  const factory _SearchState(
      {required final String query,
      required final bool isLoading,
      required final bool hasMore,
      required final int page,
      required final List<Map<String, dynamic>> products,
      required final List<Map<String, dynamic>> categories,
      required final List<Map<String, dynamic>> subcategories,
      required final List<Map<String, dynamic>> stores,
      required final List<String> recentSearches,
      required final List<Map<String, dynamic>> suggestions,
      final String? error}) = _$SearchStateImpl;

  @override
  String get query;
  @override
  bool get isLoading;
  @override
  bool get hasMore;
  @override
  int get page;
  @override
  List<Map<String, dynamic>> get products;
  @override
  List<Map<String, dynamic>> get categories;
  @override
  List<Map<String, dynamic>> get subcategories;
  @override
  List<Map<String, dynamic>> get stores;
  @override
  List<String> get recentSearches;
  @override
  List<Map<String, dynamic>> get suggestions;
  @override
  String? get error;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchStateImplCopyWith<_$SearchStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
